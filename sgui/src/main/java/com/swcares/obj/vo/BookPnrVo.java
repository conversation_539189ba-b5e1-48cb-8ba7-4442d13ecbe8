package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 生成PNR响应VO
 *
 * <AUTHOR>
 * @date 2025/5/19 14:00
 */
@Data
@ApiModel(value = "BookPnrVo", description = "生成PNR响应VO")
public class BookPnrVo {

    @ApiModelProperty(value = "PNR编号")
    private String pnrNo;

    @ApiModelProperty(value = "PNR大编码")
    private String pnrIcs;

    @ApiModelProperty(value = "PNR状态")
    private String pnrStatus;

    @ApiModelProperty(value = "创建时间")
    private String createTime;
}
