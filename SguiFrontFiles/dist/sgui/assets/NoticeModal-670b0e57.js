import{N as u,_ as v}from"./NoticeList.vue_vue_type_script_setup_true_lang-a3648a8b.js";import{E as N}from"./index-0d57d4fc.js";import{f as h,r as t,j as g,k as y,w as _,G as s,H as x,x as r}from"./index-5ab303b6.js";import{_ as k}from"./_plugin-vue_export-helper-c27b6911.js";const M={class:"text-gray-1 text-lg font-bold leading-normal"},w={class:"notice-modal"},T={class:"notice-left"},V={class:"notice-right"},D=h({__name:"NoticeModal",setup(I){const l=t(""),n=t(!0),c=t(),d=t(),i=t(),f=(e,o)=>{var a;if(l.value===e){(a=c.value)==null||a.repeatShowMainTopicInfo();return}o&&(d.value=o),l.value=e},m=e=>{i.value.focusName(e)};return(e,o)=>{const a=N;return g(),y(a,{modelValue:n.value,"onUpdate:modelValue":o[0]||(o[0]=p=>n.value=p),width:1040,height:600,"close-on-click-modal":!1,"destroy-on-close":""},{header:_(()=>[s("span",M,x(e.$t("app.systemNotice.title")),1)]),default:_(()=>[s("div",w,[s("div",T,[r(u,{ref_key:"leftRef",ref:i,"is-modal":!0,onGetTopicName:f},null,512)]),s("div",V,[r(v,{ref_key:"noticeDetailInfoRef",ref:c,"topic-name":l.value,onFocusTopicName:m},null,8,["topic-name"])])])]),_:1},8,["modelValue"])}}});const G=k(D,[["__scopeId","data-v-f7d67e74"]]);export{G as N};
